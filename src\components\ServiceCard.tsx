import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckIcon, UsersIcon, StarIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useBooking } from '@/context/BookingContext';
import ServiceCardSavingsBanner from './ServiceCardSavingsBanner';

interface ServiceCardProps {
  id: number;
  name: string;
  image: string;
  price: number;
  currency: string;
  features: string[];
  capacity: string;
  type: string;
  vehicles?: string;
  note?: string;
  isPopular?: boolean;
  totalPassengers?: number;
  onBookNow: (serviceId: number) => void;
}

const ServiceCard = ({
  id,
  name,
  image,
  price,
  currency,
  features,
  capacity,
  type,
  vehicles,
  note,
  isPopular = false,
  totalPassengers,
  onBookNow
}: ServiceCardProps) => {
  const navigate = useNavigate();
  const { state } = useBooking();

  const handleBookNow = () => {
    const serviceData = {
      id,
      name,
      image,
      price,
      currency,
      features,
      capacity,
      type,
      note,
      isPopular
    };
    
    navigate('/booking-details', { state: serviceData });
    onBookNow(id);

    
  };
  return (
    <Card className={`overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ${
      isPopular ? 'ring-2 ring-yellow-400 shadow-lg' : ''
    }`}>
      {isPopular && (
        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-center py-2 font-bold text-sm">
          <StarIcon className="w-4 h-4 inline mr-1" />
          BEST VALUE
        </div>
      )}
      
      <CardContent className="p-6">
        <div className="grid md:grid-cols-3 gap-6 items-start">
          {/* Vehicle Image */}
          <div className="flex justify-center">
            <div className="relative">
              <img 
                src={image} 
                alt={name}
                className="w-full h-auto object-cover hover:scale-110 transition-transform duration-300"
              />
              <div className="absolute -top-2 -right-2">
                <Badge variant="secondary" className="text-xs">
                  <UsersIcon className="w-3 h-3 mr-1" />
                  {capacity} pax
                  {note && note.includes('2 Services') ? '- 2 services' : ''}
                </Badge>
              </div>
            </div>
          </div>

          {/* Service Details */}
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-bold text-foreground mb-2">{name}</h3>
              <p>{vehicles}</p>
              <Badge variant="outline" className="mb-3">
                {type}
              </Badge>
            </div>
            
            <div className="space-y-2">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start gap-2 text-sm text-muted-foreground">
                  <CheckIcon className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>

            {/* {note && (
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="text-xs text-orange-700 font-medium">
                  ⚠️ {note}
                </div>
              </div>
            )} */}
          </div>

          {/* Pricing and Booking */}
          <div className="text-center space-y-4">
            <div className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl">
              <div className="font-bold text-sm text-muted-foreground mb-1">{type} - {note ? note : 'One-Way Rate'}</div>
              <div className="text-4xl font-bold text-foreground mb-1">
                ${price}
                <span className="text-lg font-normal text-muted-foreground ml-1">{currency}</span>
              </div>
              <div className="text-xs text-muted-foreground">Taxes included</div>
              <div className="text-xs text-muted-foreground">
                {note && note.includes('2 Services') ?
                  `Total for 2 vehicles (${capacity} each)` :
                  totalPassengers ?
                    `Total for ${totalPassengers} passenger${totalPassengers !== 1 ? 's' : ''}` :
                    <b>Total for {capacity}</b>
                }
              </div>
            </div>

            {/* Round Trip Savings Banner */}
            <ServiceCardSavingsBanner
              serviceId={id}
              currentPrice={price}
              isRoundTrip={state.roundTrip}
              serviceName={name}
              serviceCapacity={capacity}
            />

            <Button
              className={`w-full font-bold py-3 transition-all duration-300 ${
                isPopular 
                  ? 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white shadow-lg' 
                  : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white'
              }`}
              onClick={handleBookNow}
            >
              {isPopular ? '⭐ BOOK NOW' : 'BOOK NOW'}
            </Button>

            {isPopular && (
              <div className="text-xs text-yellow-600 font-medium">
                🔥 Best value for money
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ServiceCard;