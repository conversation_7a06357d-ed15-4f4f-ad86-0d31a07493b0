import { useState } from 'react';
import { HotelSearch } from '@/features/hotels';
import type { Hotel } from '@/features/hotels';

function TestHotelSearch() {
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  const [searchValue, setSearchValue] = useState('');

  const handleHotelChange = (value: string, hotel?: Hotel) => {
    setSearchValue(value);
    setSelectedHotel(hotel || null);
    console.log('Hotel changed:', { value, hotel });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 to-blue-800 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">Test Hotel Search</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search for Hotels (try "Corazon")
            </label>
            <HotelSearch
              value={searchValue}
              onChange={handleHotelChange}
              placeholder="Search hotels..."
              className="w-full"
            />
          </div>

          {selectedHotel && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="font-semibold text-green-800">Selected Hotel:</h3>
              <p><strong>ID:</strong> {selectedHotel.id}</p>
              <p><strong>Name:</strong> {selectedHotel.name}</p>
              <p><strong>Zone ID:</strong> {selectedHotel.zone_id}</p>
              <p><strong>Zone Name:</strong> {selectedHotel.zone_name}</p>
              <p><strong>Address:</strong> {selectedHotel.address}</p>
            </div>
          )}

          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="font-semibold text-blue-800">Current Values:</h3>
            <p><strong>Search Value:</strong> {searchValue}</p>
            <p><strong>Has Hotel:</strong> {selectedHotel ? 'Yes' : 'No'}</p>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Instructions:</strong></p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Try searching for "Corazon"</li>
              <li>Check browser console for debug logs</li>
              <li>Verify hotel appears in dropdown</li>
              <li>Select hotel and check details above</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TestHotelSearch;
